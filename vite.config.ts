import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { fileURLToPath, URL } from "node:url";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    allowedHosts: ["596506f3-0fc3-471e-8598-a2124ea4e5f8.lovableproject.com"],
    fs: {
      allow: ['..']
    },
    // Add history API fallback for SPA routing
    proxy: {
      // This ensures that any path that doesn't match a static asset
      // will serve index.html, allowing client-side routing to work on refresh
      '/blog': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      },
      '/guides': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      }
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  assetsInclude: ['**/*.md'],
}));
