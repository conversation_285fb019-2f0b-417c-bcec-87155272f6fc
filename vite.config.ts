import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { fileURLToPath, URL } from "node:url";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    allowedHosts: ["596506f3-0fc3-471e-8598-a2124ea4e5f8.lovableproject.com"],
    fs: {
      allow: ['..']
    },
    // Add history API fallback for SPA routing
    proxy: {
      // This ensures that any path that doesn't match a static asset
      // will serve index.html, allowing client-side routing to work on refresh
      '/blog': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      },
      '/guides': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: () => '/index.html'
      }
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  assetsInclude: ['**/*.md'],
  build: {
    // Aggressive bundle splitting for better performance
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'ui-vendor': ['framer-motion', 'lucide-react'],
          'query-vendor': ['@tanstack/react-query'],

          // Feature chunks
          'charts': ['recharts'],
          'forms': ['react-hook-form'],
          'animations': ['framer-motion'],

          // Third-party chunks
          'analytics': ['posthog-js', '@vercel/analytics', '@vercel/speed-insights'],

          // Large components
          'blog': ['src/pages/Blog.tsx', 'src/content/index.ts'],
          'simulator': ['src/pages/BettingSimulator.tsx'],
          'guides': ['src/pages/Guides.tsx', 'src/pages/GuideDetail.tsx'],
        },
        // Optimize chunk size
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '') : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
      },
    },
    // Optimize build performance
    target: 'esnext',
    minify: 'esbuild',
    cssMinify: true,
    // Split CSS for better loading
    cssCodeSplit: true,
    // Optimize asset handling
    assetsInlineLimit: 4096, // Inline small assets
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react',
      '@tanstack/react-query'
    ],
    exclude: [
      // Exclude heavy dependencies from pre-bundling
      'recharts',
      'posthog-js'
    ],
  },
}));
