#!/bin/bash

# <PERSON><PERSON>t to clean up original testimonial images after confirming the renamed ones work properly

echo "This script will remove the backup of original testimonial images."
echo "Only run this after confirming the website works correctly with the renamed images."
echo ""
read -p "Are you sure you want to proceed? (y/n): " confirm

if [ "$confirm" != "y" ]; then
  echo "Operation cancelled."
  exit 0
fi

# Remove the backup directory with original images
rm -rf /Users/<USER>/Desktop/Developer/datawise-website/public/lovable-uploads/backup

echo "Original testimonial image backups have been removed."
echo "If you need to restore the original images, you'll need to revert your git changes or restore from another backup."
