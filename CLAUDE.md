# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Build for development (with source maps)
npm run build:dev

# Preview production build locally
npm run preview

# Run linting
npm run lint
```

**Note:** No test commands are configured. The project doesn't have a testing framework set up.

## High-Level Architecture

This is a React-based sports betting analytics website built with:
- **React 18** with TypeScript for type safety
- **Vite** as the build tool
- **Tailwind CSS** with shadcn/ui component library
- **React Router v7** for client-side routing
- **TanStack Query** for server state management
- **PostHog**, Vercel Analytics for tracking

### Key Architectural Patterns

1. **Component Structure**: Components are organized by feature in `/src/components/` with UI primitives in `/src/components/ui/`
2. **Lazy Loading**: Routes use React.lazy() for code splitting
3. **Markdown-based Blog**: Blog posts are stored as Markdown files in `/src/content/blog/` with frontmatter metadata
4. **Type Safety**: Full TypeScript coverage with strict mode enabled
5. **Path Aliases**: Use `@/*` to import from `src/*` directory

### Project Structure Overview

- **Pages** (`/src/pages/`): Route-level components (Index, Blog, Guides, BettingSimulator)
- **Blog System**: Markdown files parsed with gray-matter and marked libraries
- **Styling**: Modular CSS approach with separate files for animations, effects, and utilities
- **Assets**: Self-hosted fonts and optimized images in `/public/`

### Important Context

- This is a Lovable project (originally created via lovable.dev)
- The site is deployed on Netlify with SPA fallback configuration
- PostHog analytics is integrated with specific project credentials
- The project includes a planned CMS workflow (see cms.md) but not yet implemented

### Performance Considerations

- Images are optimized to WebP format
- Fonts are self-hosted and preloaded
- Components use intersection observer for lazy loading
- CSS animations are GPU-accelerated where possible