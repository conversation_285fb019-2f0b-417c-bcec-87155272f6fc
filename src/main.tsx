import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Function to prioritize rendering on mobile
const renderApp = () => {
  const rootElement = document.getElementById("root");
  if (rootElement) {
    const root = createRoot(rootElement);
    root.render(<App />);
  }
};

// Check if we're on a mobile device - a simple approach to avoid TypeScript issues
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// For mobile devices, slight delay to ensure browser finishes painting
if (isMobileDevice()) {
  // Small timeout to allow the browser to prioritize painting
  setTimeout(renderApp, 50);
} else {
  // For desktop, render immediately
  renderApp();
}
