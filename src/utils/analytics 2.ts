import posthog from 'posthog-js';

// User identification and properties
export const identifyUser = (userId: string, userProperties?: Record<string, any>) => {
  if (userId) {
    posthog.identify(userId, userProperties);
  }
};

// Track specific events with properties
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  posthog.capture(eventName, properties);
};

// Common analytics events
export const Analytics = {
  // Content engagement events
  trackArticleView: (articleId: string, title: string, author: string, category: string) => {
    trackEvent('article_view', { article_id: articleId, title, author, category });
  },
  
  trackArticleRead: (articleId: string, title: string, readTimeSeconds: number, readPercentage: number) => {
    trackEvent('article_read', { 
      article_id: articleId, 
      title, 
      read_time_seconds: readTimeSeconds,
      read_percentage: readPercentage
    });
  },
  
  // User interaction events
  trackButtonClick: (buttonName: string, buttonLocation: string) => {
    trackEvent('button_click', { button_name: buttonName, location: buttonLocation });
  },
  
  trackLinkClick: (linkText: string, linkUrl: string, linkLocation: string) => {
    trackEvent('link_click', { link_text: linkText, url: linkUrl, location: linkLocation });
  },
  
  // Form interactions
  trackFormSubmit: (formName: string, formLocation: string, success: boolean) => {
    trackEvent('form_submit', { form_name: formName, location: formLocation, success });
  },
  
  trackFormFieldInteraction: (formName: string, fieldName: string, action: 'focus' | 'blur' | 'change') => {
    trackEvent('form_field_interaction', { form_name: formName, field_name: fieldName, action });
  },
  
  // Betting simulator events
  trackSimulationStart: (parameters: Record<string, any>) => {
    trackEvent('simulation_start', parameters);
  },
  
  trackSimulationComplete: (parameters: Record<string, any>, results: Record<string, any>) => {
    trackEvent('simulation_complete', { ...parameters, ...results });
  },
  
  // User journey
  trackUserSignUp: (method: string) => {
    trackEvent('user_sign_up', { method });
  },
  
  trackUserLogIn: (method: string) => {
    trackEvent('user_log_in', { method });
  },
  
  // Feature usage
  trackFeatureUsage: (featureName: string, details?: Record<string, any>) => {
    trackEvent('feature_used', { feature_name: featureName, ...details });
  },
  
  // Errors and issues
  trackError: (errorType: string, errorMessage: string, errorContext?: Record<string, any>) => {
    trackEvent('error_occurred', { error_type: errorType, error_message: errorMessage, ...errorContext });
  }
};

// Set global properties that will be sent with every event
export const setGlobalProperties = (properties: Record<string, any>) => {
  posthog.register(properties);
};

// Manage feature flags
export const isFeatureEnabled = (flagName: string) => {
  return posthog.isFeatureEnabled(flagName);
};

export default Analytics;
