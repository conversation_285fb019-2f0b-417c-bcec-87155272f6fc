// This file serves as a direct export of blog content
// to help debug issues with the import.meta.glob pattern

import understandingLuck from './blog/does-luck-affect-sports-betting.md?raw';
import avoidLimits from './blog/how-to-avoid-sportsbook-limits.md?raw';
import deviggingOdds from './blog/devigging-sportsbook-odds.md?raw';
import positiveEV from './blog/positive-expected-value-betting-beginner-guide.md?raw';
import profitableBetting from './blog/profitable-sports-betting-strategy-with-positive-expected-value.md?raw';

export const blogContent = {
  'does-luck-affect-sports-betting': understandingLuck,
  'how-to-avoid-sportsbook-limits': avoidLimits,
  'devigging-sportsbook-odds': deviggingOdds,
  'positive-expected-value-betting-beginner-guide': positiveEV,
  'profitable-sports-betting-strategy-with-positive-expected-value': profitableBetting
};

export default blogContent;