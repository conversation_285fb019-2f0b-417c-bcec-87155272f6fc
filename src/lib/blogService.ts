import { BlogPost, categories as defaultCategories, blogPosts as hardcodedPosts } from '@/data/blog-data';
import { parseMarkdown, pathToSlug, markdownToBlogPost } from './parseMarkdown';
import { blogContent } from '@/content';

/**
 * Gets all blog posts from markdown files
 */
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    console.log('===== BLOG SERVICE DEBUGGING =====');
    
    // First try to load posts using dynamic imports (most reliable)
    const markdownFiles = import.meta.glob('../content/blog/*.md', { 
      query: '?raw',
      import: 'default',
      eager: false // Load on demand
    });
    
    console.log('Markdown files found:', Object.keys(markdownFiles).length);
    
    let markdownPosts: BlogPost[] = [];
    
    // Process each markdown file
    if (Object.keys(markdownFiles).length > 0) {
      console.log('Processing markdown files from glob pattern');
      
      // Process the files
      const processPromises = Object.entries(markdownFiles).map(async ([path, importFn]) => {
        try {
          // Extract the slug from the path
          const slug = pathToSlug(path);
          console.log(`Processing file: ${path} -> slug: ${slug}`);
          
          // Load the content
          const content = await importFn();
          
          if (!content || typeof content !== 'string') {
            console.error(`Invalid content for ${path}`);
            return null;
          }
          
          console.log(`Content loaded for ${slug}, length: ${content.length} chars`);
          
          // Parse the markdown
          try {
            const { frontmatter, contentHtml } = await parseMarkdown(content);
            
            // Check if frontmatter exists and has required fields
            if (!frontmatter || !frontmatter.title) {
              console.error(`Missing or invalid frontmatter for ${slug}`);
              return null;
            }
            
            // Convert to blog post
            const post = markdownToBlogPost(slug, frontmatter, contentHtml);
            console.log(`Successfully processed post: ${post.title}`);
            return post;
          } catch (parseError) {
            console.error(`Error parsing markdown for ${slug}:`, parseError);
            return null;
          }
        } catch (fileError) {
          console.error(`Error processing file ${path}:`, fileError);
          return null;
        }
      });
      
      // Wait for all files to be processed
      const results = await Promise.all(processPromises);
      
      // Filter out null results and add valid posts
      markdownPosts = results.filter(post => post !== null) as BlogPost[];
      
      console.log('Successfully processed markdown posts:', markdownPosts.length);
      
      // Return markdown posts if any were successfully loaded
      if (markdownPosts.length > 0) {
        console.log('Using markdown posts from glob pattern');
        
        // Sort posts by date, newest first
        return markdownPosts.sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateB.getTime() - dateA.getTime();
        });
      }
    }
    
    // If dynamic imports failed, try direct imports
    if (markdownPosts.length === 0 && Object.keys(blogContent).length > 0) {
      console.log('Dynamic imports failed, trying direct imports from content/index.ts');
      
      for (const slug in blogContent) {
        try {
          const content = blogContent[slug];
          
          if (!content || typeof content !== 'string') {
            console.error(`Invalid content for ${slug}`);
            continue;
          }
          
          console.log(`Processing direct import: ${slug}, content length: ${content.length}`);
          
          // Parse the markdown
          try {
            const { frontmatter, contentHtml } = await parseMarkdown(content);
            
            // Check if frontmatter exists and has required fields
            if (!frontmatter || !frontmatter.title) {
              console.error(`Missing or invalid frontmatter for ${slug}`);
              continue;
            }
            
            // Convert to blog post
            const post = markdownToBlogPost(slug, frontmatter, contentHtml);
            markdownPosts.push(post);
            console.log(`Successfully processed post: ${post.title}`);
          } catch (parseError) {
            console.error(`Error parsing markdown for ${slug}:`, parseError);
          }
        } catch (importError) {
          console.error(`Error processing direct import ${slug}:`, importError);
        }
      }
      
      // Return direct import posts if any were successfully loaded
      if (markdownPosts.length > 0) {
        console.log('Using posts from direct imports');
        
        // Sort posts by date, newest first
        return markdownPosts.sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          return dateB.getTime() - dateA.getTime();
        });
      }
    }
    
    // As a last resort, fall back to hardcoded posts
    console.log('Failed to load any markdown posts, falling back to hardcoded posts');
    return hardcodedPosts;
    
  } catch (error) {
    console.error('Error loading blog posts:', error);
    console.log('Error occurred, falling back to hardcoded posts');
    return hardcodedPosts; // Fallback to hardcoded posts in case of error
  }
}

/**
 * Get a single blog post by slug
 */
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  const posts = await getAllBlogPosts();
  return posts.find(post => post.id === slug) || null;
}

/**
 * Get all categories from blog posts
 */
export async function getAllCategories(): Promise<string[]> {
  const posts = await getAllBlogPosts();
  
  // Extract all categories from posts and remove duplicates
  const categoriesFromPosts = Array.from(
    new Set(posts.flatMap(post => post.categories))
  );
  
  // If no categories found in posts, return default categories
  return categoriesFromPosts.length > 0 ? categoriesFromPosts : defaultCategories;
}

/**
 * Get featured blog posts
 */
export async function getFeaturedPosts(limit = 3): Promise<BlogPost[]> {
  const posts = await getAllBlogPosts();
  return posts.filter(post => post.featured).slice(0, limit);
}

/**
 * Get related posts for a given post
 */
export async function getRelatedPosts(
  postId: string, 
  limit = 3
): Promise<BlogPost[]> {
  const currentPost = await getBlogPostBySlug(postId);
  if (!currentPost) return [];
  
  const allPosts = await getAllBlogPosts();
  
  // Find posts that share categories with the current post
  const relatedPosts = allPosts
    .filter(post => 
      post.id !== postId && 
      post.categories.some(cat => currentPost.categories.includes(cat))
    )
    .slice(0, limit);
    
  return relatedPosts;
}