import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import remarkGfm from 'remark-gfm'
import { BlogPost } from '@/data/blog-data'
import { <PERSON><PERSON><PERSON> } from 'buffer'    // <-- Add this

// Add a global polyfill for <PERSON><PERSON><PERSON> if needed
if (typeof globalThis !== 'undefined' && !(globalThis as any).Buffer) {
  (globalThis as any).Buffer = Buffer
}

// Define type for frontmatter
export interface BlogFrontmatter {
  title?: string;
  date?: string;
  readTime?: string;
  image?: string;
  excerpt?: string;
  author?: {
    name?: string;
    avatar?: string;
    title?: string;
  };
  categories?: string[];
  featured?: boolean;
  [key: string]: unknown;
}

/**
 * Parse markdown content and return frontmatter and processed HTML content
 */
export async function parseMarkdown(content: string): Promise<{
  frontmatter: BlogFrontmatter
  contentHtml: string
}> {
  // Extract frontmatter and markdown content
  const { data, content: markdownContent } = matter(content);
  
  // Process the markdown content to HTML
  const processedContent = await remark()
    .use(html, { 
      sanitize: false // Allow HTML in markdown
    })
    .use(remarkGfm) // GitHub flavored markdown (tables, etc)
    .process(markdownContent);
    
  let contentHtml = processedContent.toString();
  
  // Add additional processing
  contentHtml = enhanceCodeBlocks(contentHtml);
  contentHtml = cleanupHeadings(contentHtml);
  
  return {
    frontmatter: data as BlogFrontmatter,
    contentHtml
  };
}

/**
 * Enhance code blocks for better styling
 */
function enhanceCodeBlocks(html: string): string {
  // Add language labels to code blocks
  return html.replace(
    /<pre><code class="(language-[^"]*)">([\s\S]*?)<\/code><\/pre>/g,
    (match, className, codeContent) => {
      // Extract language name from class
      const language = className.replace('language-', '');
      
      // Add language label at the top of code block
      return `<pre class="${className}"><code class="${className}">
        ${language !== 'none' && language !== '' ? `<span class="code-language">${language}</span>` : ''}
        ${codeContent}
      </code></pre>`;
    }
  );
}

/**
 * Properly handle markdown headings (h1-h6)
 */
function cleanupHeadings(html: string): string {
  // First, handle h1 (usually title)
  let cleanedHtml = html.replace(/<p># (.*?)<\/p>/g, '<h1 class="text-3xl md:text-4xl font-bold mb-8 text-white">$1</h1>');
  
  // Handle h2
  cleanedHtml = cleanedHtml.replace(/<p>## (.*?)<\/p>/g, '<h2 class="text-2xl font-bold mt-10 mb-6 text-gold/90">$1</h2>');
  
  // Handle h3
  cleanedHtml = cleanedHtml.replace(/<p>### (.*?)<\/p>/g, '<h3 class="text-xl font-semibold mt-8 mb-5 text-white/90">$1</h3>');
  
  // Handle h4
  cleanedHtml = cleanedHtml.replace(/<p>#### (.*?)<\/p>/g, '<h4 class="text-lg font-medium mt-6 mb-4 text-gold/80">$1</h4>');
  
  // Handle h5
  cleanedHtml = cleanedHtml.replace(/<p>##### (.*?)<\/p>/g, '<h5 class="text-base font-medium mt-4 mb-3 text-white/80">$1</h5>');
  
  // Handle h6
  cleanedHtml = cleanedHtml.replace(/<p>###### (.*?)<\/p>/g, '<h6 class="text-sm font-medium mt-4 mb-2 text-white/70">$1</h6>');
  
  // Additionally, add style to any plain h1-h6 tags that might have been generated differently
  cleanedHtml = cleanedHtml.replace(/<h1>(.*?)<\/h1>/g, '<h1 class="text-3xl md:text-4xl font-bold mb-8 text-white">$1</h1>');
  cleanedHtml = cleanedHtml.replace(/<h2>(.*?)<\/h2>/g, '<h2 class="text-2xl font-bold mt-10 mb-6 text-gold/90">$1</h2>');
  cleanedHtml = cleanedHtml.replace(/<h3>(.*?)<\/h3>/g, '<h3 class="text-xl font-semibold mt-8 mb-5 text-white/90">$1</h3>');
  cleanedHtml = cleanedHtml.replace(/<h4>(.*?)<\/h4>/g, '<h4 class="text-lg font-medium mt-6 mb-4 text-gold/80">$1</h4>');
  cleanedHtml = cleanedHtml.replace(/<h5>(.*?)<\/h5>/g, '<h5 class="text-base font-medium mt-4 mb-3 text-white/80">$1</h5>');
  cleanedHtml = cleanedHtml.replace(/<h6>(.*?)<\/h6>/g, '<h6 class="text-sm font-medium mt-4 mb-2 text-white/70">$1</h6>');
  
  // Add more spacing between paragraphs
  cleanedHtml = cleanedHtml.replace(/<p>/g, '<p class="mb-6">');
  
  // Add spacing after <br> tags
  cleanedHtml = cleanedHtml.replace(/<br>/g, '<br class="mb-4">');
  
  // Add spacing between lists and other elements
  cleanedHtml = cleanedHtml.replace(/<\/ul>/g, '</ul><div class="mb-6"></div>');
  cleanedHtml = cleanedHtml.replace(/<\/ol>/g, '</ol><div class="mb-6"></div>');
  
  return cleanedHtml;
}

/**
 * Converts a markdown file path to a slug
 */
export function pathToSlug(path: string): string {
  // Example: '/path/to/my-blog-post.md' -> 'my-blog-post'
  return path.split('/').pop()?.replace(/\.md$/, '') || '';
}

/**
 * Convert processed markdown file to BlogPost type
 */
export function markdownToBlogPost(
  slug: string,
  frontmatter: BlogFrontmatter,
  contentHtml: string
): BlogPost {
  return {
    id: slug,
    title: frontmatter.title || 'Untitled',
    date: frontmatter.date || new Date().toLocaleDateString(),
    readTime: frontmatter.readTime || '3 min read',
    image: frontmatter.image || '/default-image.webp',
    excerpt: frontmatter.excerpt || '',
    content: contentHtml,
    author: {
      name: frontmatter.author?.name || 'Anonymous',
      avatar: frontmatter.author?.avatar || '/default-avatar.webp',
      title: frontmatter.author?.title || '',
    },
    categories: frontmatter.categories || [],
    featured: frontmatter.featured || false,
  };
} 