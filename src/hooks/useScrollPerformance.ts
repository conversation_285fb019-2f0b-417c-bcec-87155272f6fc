/**
 * Scroll Performance Optimization Hook
 * 
 * Provides utilities to maintain 60fps scrolling performance by:
 * - Throttling scroll events
 * - Using passive event listeners
 * - Implementing requestAnimationFrame for smooth updates
 * - Deferring non-critical operations during scroll
 */

import { useEffect, useRef, useCallback, useState } from 'react';

interface ScrollPerformanceOptions {
  throttleMs?: number;
  enablePassiveListeners?: boolean;
  deferNonCriticalOperations?: boolean;
  enableVirtualization?: boolean;
}

interface ScrollState {
  scrollY: number;
  scrollDirection: 'up' | 'down' | 'none';
  isScrolling: boolean;
  scrollVelocity: number;
}

export function useScrollPerformance(options: ScrollPerformanceOptions = {}) {
  const {
    throttleMs = 16, // ~60fps
    enablePassiveListeners = true,
    deferNonCriticalOperations = true,
    enableVirtualization = true,
  } = options;

  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollY: 0,
    scrollDirection: 'none',
    isScrolling: false,
    scrollVelocity: 0,
  });

  const lastScrollY = useRef(0);
  const lastScrollTime = useRef(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const rafRef = useRef<number>();
  const isScrollingRef = useRef(false);

  // Throttled scroll handler using requestAnimationFrame
  const handleScroll = useCallback(() => {
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    rafRef.current = requestAnimationFrame(() => {
      const currentScrollY = window.scrollY;
      const currentTime = performance.now();
      const deltaY = currentScrollY - lastScrollY.current;
      const deltaTime = currentTime - lastScrollTime.current;
      
      const velocity = deltaTime > 0 ? Math.abs(deltaY) / deltaTime : 0;
      const direction = deltaY > 0 ? 'down' : deltaY < 0 ? 'up' : 'none';

      setScrollState({
        scrollY: currentScrollY,
        scrollDirection: direction,
        isScrolling: true,
        scrollVelocity: velocity,
      });

      lastScrollY.current = currentScrollY;
      lastScrollTime.current = currentTime;
      isScrollingRef.current = true;

      // Clear the scrolling state after a delay
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        setScrollState(prev => ({ ...prev, isScrolling: false }));
        isScrollingRef.current = false;
      }, 150);
    });
  }, []);

  // Throttled version for high-frequency operations
  const throttledHandleScroll = useCallback(
    throttle(handleScroll, throttleMs),
    [handleScroll, throttleMs]
  );

  useEffect(() => {
    const scrollHandler = enablePassiveListeners 
      ? throttledHandleScroll 
      : handleScroll;

    const options = enablePassiveListeners ? { passive: true } : false;
    
    window.addEventListener('scroll', scrollHandler, options);
    
    // Initial scroll position
    handleScroll();

    return () => {
      window.removeEventListener('scroll', scrollHandler);
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [throttledHandleScroll, handleScroll, enablePassiveListeners]);

  // Defer operations during scroll for better performance
  const deferDuringScroll = useCallback((operation: () => void, delay = 100) => {
    if (deferNonCriticalOperations && isScrollingRef.current) {
      // Defer the operation until scrolling stops
      const checkScrolling = () => {
        if (!isScrollingRef.current) {
          operation();
        } else {
          setTimeout(checkScrolling, delay);
        }
      };
      setTimeout(checkScrolling, delay);
    } else {
      operation();
    }
  }, [deferNonCriticalOperations]);

  // Check if element is in viewport (for virtualization)
  const isInViewport = useCallback((element: HTMLElement, margin = 0) => {
    if (!enableVirtualization) return true;
    
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    return (
      rect.bottom >= -margin &&
      rect.top <= windowHeight + margin
    );
  }, [enableVirtualization]);

  // Optimized intersection observer for scroll performance
  const createOptimizedObserver = useCallback((
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ) => {
    const optimizedCallback = (entries: IntersectionObserverEntry[]) => {
      // Use requestIdleCallback for non-critical operations
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => callback(entries), { timeout: 100 });
      } else {
        setTimeout(() => callback(entries), 0);
      }
    };

    return new IntersectionObserver(optimizedCallback, {
      rootMargin: '200px 0px',
      threshold: [0, 0.1, 0.5, 1],
      ...options,
    });
  }, []);

  return {
    scrollState,
    deferDuringScroll,
    isInViewport,
    createOptimizedObserver,
    isScrolling: scrollState.isScrolling,
    scrollDirection: scrollState.scrollDirection,
    scrollVelocity: scrollState.scrollVelocity,
  };
}

// Throttle utility function
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Hook for optimizing scroll-based animations
export function useScrollAnimation(
  callback: (scrollY: number, direction: 'up' | 'down' | 'none') => void,
  deps: any[] = []
) {
  const { scrollState } = useScrollPerformance();
  const rafRef = useRef<number>();

  useEffect(() => {
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    rafRef.current = requestAnimationFrame(() => {
      callback(scrollState.scrollY, scrollState.scrollDirection);
    });

    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [scrollState.scrollY, scrollState.scrollDirection, ...deps]);
}

// Hook for virtual scrolling implementation
export function useVirtualScrolling(
  itemHeight: number,
  containerHeight: number,
  totalItems: number,
  overscan = 5
) {
  const { scrollState } = useScrollPerformance();
  
  const startIndex = Math.max(
    0,
    Math.floor(scrollState.scrollY / itemHeight) - overscan
  );
  
  const endIndex = Math.min(
    totalItems - 1,
    Math.ceil((scrollState.scrollY + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = endIndex - startIndex + 1;
  const offsetY = startIndex * itemHeight;

  return {
    startIndex,
    endIndex,
    visibleItems,
    offsetY,
    totalHeight: totalItems * itemHeight,
  };
}

// Hook for scroll-based resource loading
export function useScrollBasedLoading(threshold = 0.8) {
  const { scrollState } = useScrollPerformance();
  const [shouldLoadMore, setShouldLoadMore] = useState(false);

  useEffect(() => {
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    const scrollProgress = (scrollState.scrollY + windowHeight) / documentHeight;

    if (scrollProgress >= threshold && !shouldLoadMore) {
      setShouldLoadMore(true);
    }
  }, [scrollState.scrollY, threshold, shouldLoadMore]);

  const resetLoadMore = useCallback(() => {
    setShouldLoadMore(false);
  }, []);

  return { shouldLoadMore, resetLoadMore };
}

export default useScrollPerformance;
