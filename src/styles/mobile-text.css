/* Mobile text size improvements */

@media (max-width: 767px) {
  /* General text size increases */
  p, li, div {
    font-size: 1rem !important; /* Base text size */
    line-height: 1.5 !important;
  }
  
  /* Headings */
  h1, .text-2xl, .text-3xl, .sm\:text-3xl {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }
  
  h2, .text-xl, .sm\:text-2xl {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }
  
  h3, .text-lg, .sm\:text-xl {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }
  
  /* Specific text elements */
  .text-xs {
    font-size: 0.875rem !important; /* Increase from xs to sm */
  }
  
  .sm\:text-sm {
    font-size: 1rem !important; /* Increase from sm to base */
  }
  
  /* Button text */
  button, a.btn-primary, a.btn-outline, .btn-primary, .btn-outline {
    font-size: 1rem !important;
    padding: 0.75rem 1.25rem !important;
  }
  
  /* Stats and numbers */
  .text-lg, .sm\:text-2xl {
    font-size: 1.5rem !important;
  }
  
  /* Footer and smaller text */
  footer p, .text-gray-500, .italic {
    font-size: 0.875rem !important;
  }
  
  /* Navigation links */
  .nav-link {
    font-size: 1rem !important;
  }
  
  /* Testimonials */
  .testimonial-text {
    font-size: 1rem !important;
  }
  
  /* FAQ text */
  .faq-question {
    font-size: 1.125rem !important;
  }
  
  .faq-answer {
    font-size: 1rem !important;
  }
}
