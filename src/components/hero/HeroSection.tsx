import { useState, useEffect, lazy, Suspense } from "react";
import { testimonials } from "../testimonials/testimonialData";
import HeroContent from "./HeroContent";
import HeroImage from "./HeroImage";

// Lazy load non-critical components
const BackgroundElements = lazy(() => import("./BackgroundElements"));
const CountdownPopup = lazy(() => import("../countdown").then(mod => ({ default: mod.CountdownPopup })));

const HeroSection = () => {
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [showCountdown, setShowCountdown] = useState(false);
  const [deferredLoaded, setDeferredLoaded] = useState(false);
  
  // Load critical content first
  useEffect(() => {
    // Defer non-critical operations until after first paint
    const timer = setTimeout(() => {
      setDeferredLoaded(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Separate useEffect for testimonial slider logic - deferred
  useEffect(() => {
    if (!deferredLoaded) return;
    
    // Simple 5s auto-slide for testimonials.
    const interval = setInterval(() => {
      setCurrentTestimonialIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);
    
    return () => {
      clearInterval(interval);
    };
  }, [deferredLoaded]);

  // Separate useEffect for popup - significantly delayed
  useEffect(() => {
    if (!deferredLoaded) return;
    
    // Increased delay to 8s to ensure main content renders first
    const popupTimer = setTimeout(() => {
      const hasSeenPopup = localStorage.getItem('hasSeenTrialPopup');
      if (!hasSeenPopup) {
        setShowCountdown(true);
      }
    }, 8000);
    
    return () => {
      clearTimeout(popupTimer);
    };
  }, [deferredLoaded]);

  // Simple scroll helper
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleStartTrial = () => {
    localStorage.setItem('hasSeenTrialPopup', 'true');
  };

  const handleCloseCountdown = () => {
    localStorage.setItem('hasSeenTrialPopup', 'true');
    setShowCountdown(false);
  };

  return (
    <section className="relative w-full overflow-hidden px-0 sm:px-4 pt-24 pb-12 md:pt-36 md:pb-16">
      {/* Decorative background - lazy loaded */}
      {deferredLoaded && (
        <Suspense fallback={null}>
          <BackgroundElements />
        </Suspense>
      )}

      <div className="container mx-auto px-4 sm:px-0">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between md:gap-12">
          {/* The text content */}
          <HeroContent 
            currentTestimonialIndex={currentTestimonialIndex}
            setCurrentTestimonialIndex={setCurrentTestimonialIndex}
            scrollToSection={scrollToSection}
          />
          {/* Hero image - highest priority */}
          <HeroImage />
        </div>
      </div>

      {/* Delayed popup */}
      {showCountdown && deferredLoaded && (
        <Suspense fallback={null}>
          <CountdownPopup 
            initialTimeInHours={0.25} 
            onClose={handleCloseCountdown}
            onStartTrial={handleStartTrial}
          />
        </Suspense>
      )}
    </section>
  );
};

export default HeroSection;