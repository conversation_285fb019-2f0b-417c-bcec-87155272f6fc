import { useRef, useEffect } from "react";

const HeroImage = () => {
  const imageRef = useRef<HTMLImageElement>(null);

  // Add error handling and debugging
  useEffect(() => {
    const img = imageRef.current;
    if (img) {
      const handleLoad = () => {
        console.log('✅ Hero image loaded successfully:', img.src);
      };

      const handleError = (error: Event) => {
        console.error('❌ Hero image failed to load:', img.src, error);
      };

      img.addEventListener('load', handleLoad);
      img.addEventListener('error', handleError);

      return () => {
        img.removeEventListener('load', handleLoad);
        img.removeEventListener('error', handleError);
      };
    }
  }, []);

  // Simplify component - remove state to reduce JS execution
  return (
    <div className="mt-6 sm:mt-8 md:mt-0 w-full max-w-none sm:max-w-5xl md:flex-1 relative mb-4">
      <div
        className="w-full relative rounded-none sm:rounded-xl overflow-hidden aspect-[8/7]"
        style={{
          backgroundColor: "#111111",
          maxHeight: "90vh",
          border: "2px solid red", // Debug border to see container
        }}
      >
        {/* Simplified image loading for debugging */}
        <img
          ref={imageRef}
          src="/lovable-uploads/DatawiseLogo.webp"
          alt="Datawise Dashboard"
          className="w-full h-full object-cover sm:rounded-xl"
          width="1100"
          height="660"
          loading="eager"
          decoding="async"
          style={{ border: "2px solid blue" }} // Debug border for image
          onLoad={() => {
            console.log('✅ Hero image loaded successfully');
            console.log('Image dimensions:', imageRef.current?.naturalWidth, 'x', imageRef.current?.naturalHeight);
          }}
          onError={(e) => {
            console.error('❌ Hero image failed to load, trying PNG fallback');
            const target = e.target as HTMLImageElement;
            console.log('Failed URL:', target.src);
            if (target.src.includes('.webp')) {
              target.src = '/lovable-uploads/HeroImage-400.png';
            } else {
              console.error('❌ Both WebP and PNG failed to load');
            }
          }}
        />
      </div>
    </div>
  );
};



export default HeroImage;