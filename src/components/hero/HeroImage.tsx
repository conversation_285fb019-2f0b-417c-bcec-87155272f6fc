import { useRef } from "react";

// Add preconnect hint on component mount
const addPreconnectHint = () => {
  if (typeof document !== 'undefined') {
    // Add preconnect for the image domain
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = 'https://www.datawisebets.com';
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  }
};

const HeroImage = () => {
  const imageRef = useRef<HTMLImageElement>(null);

  // Simplify component - remove state to reduce JS execution
  return (
    <div className="mt-6 sm:mt-8 md:mt-0 w-full max-w-none sm:max-w-5xl md:flex-1 relative mb-4">
      <div
        className="w-full relative rounded-none sm:rounded-xl overflow-hidden aspect-[8/7]"
        style={{
          backgroundColor: "#111111",
          maxHeight: "90vh",
        }}
      >
        <picture>
          {/* PNG format for mobile - absolute path for better caching */}
          <source
            type="image/png"
            srcSet="https://www.datawisebets.com/lovable-uploads/HeroImage-400.png 400w"
            media="(max-width: 640px)"
          />
          {/* WebP format for desktop/tablet - absolute path for better caching */}
          <img
            ref={imageRef}
            src="https://www.datawisebets.com/lovable-uploads/HeroImage.webp"
            alt="Datawise Dashboard"
            className="w-full h-full object-cover sm:rounded-xl"
            width="1100"
            height="660"
            loading="eager"
            decoding="async"
          />
        </picture>
      </div>
    </div>
  );
};

// Call preconnect on module load rather than component mount
if (typeof window !== 'undefined') {
  addPreconnectHint();
}

export default HeroImage;