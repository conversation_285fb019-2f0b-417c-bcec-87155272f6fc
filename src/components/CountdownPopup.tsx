
import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Clock } from "lucide-react";
import { toast } from "sonner";

interface CountdownPopupProps {
  initialTimeInHours: number;
  onClose: () => void;
  onStartTrial: () => void;
}

const CountdownPopup = ({ initialTimeInHours, onClose, onStartTrial }: CountdownPopupProps) => {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [isVisible, setIsVisible] = useState(true);

  // Convert hours to milliseconds for calculation
  const calculateTimeLeft = useCallback(() => {
    // Get the stored end time or create a new one
    const storedEndTime = localStorage.getItem('trialOfferEndTime');
    const endTime = storedEndTime 
      ? parseInt(storedEndTime, 10)
      : Date.now() + (initialTimeInHours * 60 * 60 * 1000);
    
    // If we're creating a new end time, store it
    if (!storedEndTime) {
      localStorage.setItem('trialOfferEndTime', endTime.toString());
    }
    
    const difference = endTime - Date.now();
    
    if (difference <= 0) {
      // Offer expired
      return { hours: 0, minutes: 0, seconds: 0 };
    }
    
    return {
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60)
    };
  }, [initialTimeInHours]);

  useEffect(() => {
    // Initial calculation
    setTimeLeft(calculateTimeLeft());
    
    // Update timer every second
    const timer = setInterval(() => {
      const calculated = calculateTimeLeft();
      setTimeLeft(calculated);
      
      // Check if timer has expired
      if (calculated.hours === 0 && calculated.minutes === 0 && calculated.seconds === 0) {
        clearInterval(timer);
      }
    }, 1000);
    
    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  const handleClose = () => {
    // First set the local state to start exit animation
    setIsVisible(false);
    // Wait for the exit animation to complete before calling onClose
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleStartTrial = () => {
    setIsVisible(false);
    setTimeout(() => {
      onStartTrial();
    }, 300); // Wait for exit animation
    
    toast.success("Free trial initialized! Redirecting to signup...", {
      description: "You're one step away from transforming your betting strategy",
      duration: 5000,
    });
  };

  // Format time with leading zeros
  const formatTime = (value: number) => value.toString().padStart(2, '0');

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-4 right-4 z-50 max-w-sm"
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.95 }}
          transition={{ type: "spring", stiffness: 400, damping: 30 }}
        >
          <div className="glass-card p-5 rounded-xl border border-white/10 bg-black/50 backdrop-blur-lg shadow-xl">
            {/* Decorative elements */}
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              <div className="absolute -top-20 -right-20 w-60 h-60 bg-gold/10 rounded-full blur-3xl opacity-70"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gold/5 rounded-full blur-2xl"></div>
            </div>
            
            {/* Close button - Increased clickable area */}
            <button 
              onClick={handleClose}
              className="absolute top-2 right-2 text-gray-400 hover:text-white p-2 rounded-full transition-colors z-10 h-8 w-8 flex items-center justify-center"
              aria-label="Close popup"
            >
              <X size={16} />
            </button>
            
            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center space-x-2 mb-3">
                <Clock className="text-gold w-5 h-5" />
                <h3 className="text-base font-semibold text-white">Limited Time Offer</h3>
              </div>
              
              <p className="text-sm text-gray-300 mb-4">
                Your exclusive free trial offer expires soon. Start now to get full access to all VIP features.
              </p>
              
              {/* Timer display */}
              <div className="flex justify-center space-x-2 mb-5">
                <div className="flex flex-col items-center">
                  <div className="bg-black/30 px-3 py-2 rounded-md border border-white/5 font-mono text-xl font-bold text-white">
                    {formatTime(timeLeft.hours)}
                  </div>
                  <span className="text-xs text-gray-400 mt-1">Hours</span>
                </div>
                <div className="text-white font-bold text-xl flex items-center pb-5">:</div>
                <div className="flex flex-col items-center">
                  <div className="bg-black/30 px-3 py-2 rounded-md border border-white/5 font-mono text-xl font-bold text-white">
                    {formatTime(timeLeft.minutes)}
                  </div>
                  <span className="text-xs text-gray-400 mt-1">Minutes</span>
                </div>
                <div className="text-white font-bold text-xl flex items-center pb-5">:</div>
                <div className="flex flex-col items-center">
                  <div className="bg-black/30 px-3 py-2 rounded-md border border-white/5 font-mono text-xl font-bold text-white animate-pulse">
                    {formatTime(timeLeft.seconds)}
                  </div>
                  <span className="text-xs text-gray-400 mt-1">Seconds</span>
                </div>
              </div>
              
              {/* CTA Button */}
              <motion.button
                onClick={handleStartTrial}
                className="w-full py-2.5 bg-gold hover:bg-gold/90 text-black font-medium rounded-lg text-center transition-colors btn-interactive relative overflow-hidden group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="absolute inset-0 bg-white/20 animate-pulse rounded-lg"></span>
                <span className="relative">Start Your Free Trial Now</span>
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CountdownPopup;
