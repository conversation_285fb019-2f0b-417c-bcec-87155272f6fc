import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Clock } from "lucide-react";
import { toast } from "sonner";
import { CountdownTimer } from "./CountdownTimer";
import { CountdownButton } from "./CountdownButton";

interface CountdownPopupProps {
  initialTimeInHours: number;
  onClose: () => void;
  onStartTrial: () => void;
}

export const CountdownPopup = ({ initialTimeInHours, onClose, onStartTrial }: CountdownPopupProps) => {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [isVisible, setIsVisible] = useState(true);

  // Convert hours to milliseconds for calculation
  const calculateTimeLeft = useCallback(() => {
    // Get the stored end time or create a new one
    const storedEndTime = localStorage.getItem('trialOfferEndTime');
    const endTime = storedEndTime 
      ? parseInt(storedEndTime, 10)
      : Date.now() + (initialTimeInHours * 60 * 60 * 1000);
    
    // If we're creating a new end time, store it
    if (!storedEndTime) {
      localStorage.setItem('trialOfferEndTime', endTime.toString());
    }
    
    const difference = endTime - Date.now();
    
    if (difference <= 0) {
      // Offer expired
      return { hours: 0, minutes: 0, seconds: 0 };
    }
    
    return {
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60)
    };
  }, [initialTimeInHours]);

  useEffect(() => {
    // Initial calculation
    setTimeLeft(calculateTimeLeft());
    
    // Update timer every second
    const timer = setInterval(() => {
      const calculated = calculateTimeLeft();
      setTimeLeft(calculated);
      
      // Check if timer has expired
      if (calculated.hours === 0 && calculated.minutes === 0 && calculated.seconds === 0) {
        clearInterval(timer);
      }
    }, 1000);
    
    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  const handleClose = () => {
    // First set the local state to start exit animation
    setIsVisible(false);
    // Wait for the exit animation to complete before calling onClose
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleStartTrial = () => {
    setIsVisible(false);
    setTimeout(() => {
      // Open the new 10-day trial link
      window.open('https://whop.com/checkout/plan_zT7piEa5QAzvG?d2c=true', '_blank');
      onStartTrial();
    }, 300); // Wait for exit animation
    
    toast.success("Extended 10-day trial initialized! Redirecting to signup...", {
      description: "You're getting our BEST trial offer - 10 full days of premium access!",
      duration: 5000,
    });
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-4 right-4 z-50 max-w-sm sm:max-w-sm"
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.95 }}
          transition={{ type: "spring", stiffness: 400, damping: 30 }}
        >
          <div className="glass-card p-3 sm:p-5 rounded-xl border border-white/10 bg-black/50 backdrop-blur-lg shadow-xl">
            {/* Decorative elements */}
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              <div className="absolute -top-20 -right-20 w-60 h-60 bg-gold/10 rounded-full blur-3xl opacity-70"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gold/5 rounded-full blur-2xl"></div>
            </div>
            
            {/* Close button - Increased clickable area */}
            <button 
              onClick={handleClose}
              className="absolute top-0 right-0 sm:top-1 sm:right-1 text-gray-400 hover:text-white p-2 transition-colors z-20 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center cursor-pointer"
              aria-label="Close popup"
              type="button"
              style={{ touchAction: "manipulation" }}
            >
              <X size={18} className="sm:hidden" strokeWidth={2.5} />
              <X size={20} className="hidden sm:block" strokeWidth={2.5} />
            </button>
            
            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center space-x-2 mb-2 sm:mb-3">
                <Clock className="text-gold w-4 h-4 sm:w-5 sm:h-5" />
                <h3 className="text-sm sm:text-base font-semibold text-white">EXCLUSIVE: 10-Day Free Trial</h3>
              </div>
              
              <p className="text-xs sm:text-sm text-gray-300 mb-3 sm:mb-4">
                Limited time offer: Get our <span className="text-gold font-medium">extended 10-day trial</span> instead of the standard 7 days. Unlock all premium features instantly!
              </p>
              
              {/* Timer display */}
              <CountdownTimer 
                hours={timeLeft.hours}
                minutes={timeLeft.minutes}
                seconds={timeLeft.seconds}
              />
              
              {/* CTA Button */}
              <CountdownButton onClick={handleStartTrial}>
                Claim Your 10-Day Free Trial
              </CountdownButton>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CountdownPopup;
