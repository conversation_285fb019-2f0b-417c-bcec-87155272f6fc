import { ReactNode, Suspense } from 'react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

interface LazyLoadSectionProps {
  children: ReactNode;
  preloadMargin?: string;
  threshold?: number;
  fallback?: ReactNode;
  id?: string;
}

/**
 * Component that lazy loads its children when they enter the viewport
 */
export default function LazyLoadSection({
  children,
  preloadMargin = '500px',
  threshold = 0,
  fallback = (
    <div className="w-full py-12 flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  ),
  id,
}: LazyLoadSectionProps) {
  const [ref, isVisible] = useIntersectionObserver({
    rootMargin: preloadMargin,
    threshold,
  });

  return (
    <div ref={ref} id={id}>
      {isVisible ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        <div className="min-h-[100px]" />
      )}
    </div>
  );
}
