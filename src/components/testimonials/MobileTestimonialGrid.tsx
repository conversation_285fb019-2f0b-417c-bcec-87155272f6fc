
import { motion, AnimatePresence } from "framer-motion";
import { TestimonialData } from "@/types/testimonial";
import { useRef, useState } from "react";
import MemoizedTestimonial from "./MemoizedTestimonial";
import { TouchEvent } from "react";

interface MobileTestimonialGridProps {
  visibleIndices: number[];
  testimonials: TestimonialData[];
  isAnimating: boolean;
  direction: 'next' | 'prev';
  initialLoad: boolean;
  onNext?: () => void;
  onPrev?: () => void;
}

const MobileTestimonialGrid = ({ 
  visibleIndices, 
  testimonials, 
  isAnimating,
  direction,
  initialLoad,
  onNext,
  onPrev 
}: MobileTestimonialGridProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dragStartX, setDragStartX] = useState(0);

  // Handle swipe gestures on mobile
  const handleDragStart = (event: TouchEvent<HTMLDivElement>) => {
    if (isAnimating) return;
    
    // Get the starting X position of the drag
    const clientX = event.touches[0].clientX;
    setDragStartX(clientX);
  };

  const handleDragEnd = (event: TouchEvent<HTMLDivElement>) => {
    if (isAnimating) return;
    
    // Get the ending X position of the drag
    const clientX = event.changedTouches[0].clientX;
    const dragDistance = clientX - dragStartX;
    const swipeThreshold = 50; // Minimum distance required for a swipe
    
    if (Math.abs(dragDistance) > swipeThreshold) {
      if (dragDistance > 0) {
        // Swiped right, go to previous
        onPrev?.();
      } else {
        // Swiped left, go to next
        onNext?.();
      }
    }
  };

  return (
    <div 
      className="relative overflow-hidden pt-8 pb-2 swipeable-container" 
      style={{ 
        height: '720px', // Reduced height to work better with pagination
        contain: 'content',
        willChange: 'contents',
        overflowX: 'hidden',
        overscrollBehavior: 'none'
      }} 
      ref={containerRef}
      onTouchStart={handleDragStart}
      onTouchEnd={handleDragEnd}
    >
      <AnimatePresence mode="popLayout" initial={false}>
        <motion.div 
          key={visibleIndices.join('-')}
          className="grid grid-cols-2 gap-3 px-1"
          initial={initialLoad ? false : { opacity: 0, x: direction === 'next' ? 50 : -50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: direction === 'next' ? -50 : 50 }}
          transition={{ 
            duration: 1.0, 
            ease: [0.25, 0.1, 0.25, 1],
            delayChildren: 0.1,
            staggerChildren: 0.07
          }}
          style={{ 
            willChange: "transform, opacity",
            transform: "translate3d(0,0,0)",
            backfaceVisibility: "hidden",
            WebkitBackfaceVisibility: "hidden",
            perspective: 1000,
            WebkitPerspective: "1000",
            pointerEvents: isAnimating ? 'none' : 'auto',
            position: 'absolute',
            width: '100%',
            touchAction: 'pan-y'
          }}
        >
          {visibleIndices.map((index, i) => (
            <motion.div 
              key={`testimonial-${index}`} 
              className="h-full testimonial-transition"
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.7,
                delay: i * 0.08,
                ease: [0.25, 0.1, 0.25, 1]
              }}
              style={{
                willChange: "opacity",
                transform: "translate3d(0,0,0)",
                backfaceVisibility: "hidden",
                WebkitBackfaceVisibility: "hidden"
              }}
            >
              <MemoizedTestimonial
                data={testimonials[index]} 
                delay={0} 
              />
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default MobileTestimonialGrid;
