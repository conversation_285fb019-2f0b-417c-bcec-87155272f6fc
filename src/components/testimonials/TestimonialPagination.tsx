
interface TestimonialPaginationProps {
  totalPages: number;
  visibleIndices: number[];
  onPageClick: (pageIndex: number) => void;
  isAnimating: boolean;
  testimonialsPerPage: number;
}

const TestimonialPagination = ({ totalPages, visibleIndices, onPageClick, isAnimating, testimonialsPerPage }: TestimonialPaginationProps) => {
  return (
    <div className="flex justify-center mt-4 sm:mt-6 gap-2">
      {Array.from({ length: totalPages }).map((_, i) => {
        const isActive = visibleIndices.includes(i * testimonialsPerPage);
        return (
          <button
            key={`page-${i}`}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              isActive ? 'bg-gold/90 w-6' : 'bg-gold/30 hover:bg-gold/50'
            }`}
            onClick={() => {
              if (!isAnimating) {
                onPageClick(i);
              }
            }}
            aria-label={`Go to page ${i + 1}`}
          />
        );
      })}
    </div>
  );
};

export default TestimonialPagination;
