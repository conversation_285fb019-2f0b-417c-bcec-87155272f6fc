import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";

const CTA = () => {
  const scrollToPrice = () => {
    const element = document.getElementById('pricing');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="container mx-auto px-4 py-16 md:py-28">
      <motion.div 
        className="glass-card p-6 md:p-12 lg:p-16 relative overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        whileHover={{ 
          boxShadow: "0 20px 40px rgba(255, 215, 0, 0.15)"
        }}
      >
        {/* Enhanced background gradient effects */}
        <div className="absolute inset-0 bg-gradient-to-r from-gold/10 to-transparent opacity-50"></div>
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gold/10 rounded-full blur-[100px]"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gold/5 rounded-full blur-[100px]"></div>
        
        {/* Decorative dot pattern */}
        <div className="absolute top-10 left-10 w-32 h-32 dot-pattern opacity-50 -z-10"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 dot-pattern opacity-50 rotate-45 -z-10"></div>
        
        <div className="relative z-10 max-w-4xl mx-auto text-center">
          <motion.h2 
            className="text-xl sm:text-2xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 enhanced-gradient-text"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Transform Your Betting Strategy with Data
          </motion.h2>
          <motion.p 
            className="text-xs sm:text-sm md:text-lg text-gray-300 mb-6 md:mb-8 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Join 1,000+ bettors who’ve earned $1M+ in collective profits.
          </motion.p>
          <motion.div
            className="flex flex-col sm:flex-row justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <motion.a 
              href="https://whop.com/checkout/plan_NCr6hVh2qtYBb?d2c=true"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary inline-flex items-center justify-center btn-interactive relative overflow-hidden group text-xs sm:text-sm py-2 sm:py-3 px-3 sm:px-5"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="absolute inset-0 bg-gold/20 animate-pulse rounded-lg"></span>
              <span className="relative">
                Claim Your Free Trial Now
                <ArrowRight className="ml-1.5 sm:ml-2 h-4 w-4 inline group-hover:translate-x-1 transition-transform" />
              </span>
            </motion.a>
          </motion.div>
          
          {/* Added decorative element */}
          <motion.div 
            className="mt-8 md:mt-12 opacity-70"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 0.7 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="flex justify-center space-x-5 md:space-x-8">
              {Array.from({ length: 5 }).map((_, i) => (
                <div 
                  key={i} 
                  className="w-1.5 md:w-2 h-1.5 md:h-2 rounded-full bg-gold/50"
                  style={{
                    animation: `float ${3 + i * 0.2}s ease-in-out infinite`,
                    animationDelay: `${i * 0.2}s`
                  }}
                ></div>
              ))}
            </div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default CTA;
