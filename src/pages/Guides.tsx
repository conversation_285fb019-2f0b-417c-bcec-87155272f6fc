import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Clock, Search, Tag, ChevronRight, Filter } from "lucide-react";

// Import from blog service instead of direct data
import { getAllBlogPosts, getAllCategories } from "@/lib/blogService";
import { BlogPost } from "@/data/blog-data";

const Guides = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    async function loadBlogData() {
      try {
        setIsLoading(true);
        const [posts, cats] = await Promise.all([
          getAllBlogPosts(),
          getAllCategories()
        ]);
        setBlogPosts(posts);
        setCategories(cats);
      } catch (error) {
        console.error("Error loading blog data:", error);
      } finally {
        setIsLoading(false);
      }
    }
    
    loadBlogData();
  }, []);
  
  // Filter posts based on search query and selected category
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory ? post.categories.includes(selectedCategory) : true;
    return matchesSearch && matchesCategory;
  });
  
  console.log("Blog posts loaded:", blogPosts.length);
  console.log("Filtered posts:", filteredPosts.length);
  
  return (
    <div className="flex flex-col min-h-screen bg-black">
      <Navbar />
      
      <main className="flex-grow pt-32 pb-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.h1 
              className="text-4xl md:text-5xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="heading-gradient">Betting Guides & Resources</span>
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Expert analysis, strategies, and insights to improve your betting approach
            </motion.p>
          </div>
          
          {/* Search and filter section */}
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-10 gap-4">
            <div className="relative w-full md:w-1/2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 w-full rounded-lg bg-white/5 border border-white/10 focus:border-gold/50 focus:outline-none text-white focus:ring-1 focus:ring-gold/30 transition-all"
              />
            </div>
            
            <div className="flex items-center space-x-2 overflow-x-auto pb-2 w-full md:w-auto">
              <span className="text-gray-400 flex items-center">
                <Filter className="h-4 w-4 mr-1" />
                Filter:
              </span>
              <button
                onClick={() => setSelectedCategory(null)}
                className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-all ${
                  selectedCategory === null 
                    ? "bg-gold/20 text-gold border border-gold/50" 
                    : "bg-white/5 border border-white/10 hover:bg-white/10"
                }`}
              >
                All
              </button>
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-all ${
                    selectedCategory === category 
                      ? "bg-gold/20 text-gold border border-gold/50" 
                      : "bg-white/5 border border-white/10 hover:bg-white/10"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
          
          {/* Loading state */}
          {isLoading && (
            <div className="text-center py-20">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-gold border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] mb-4"></div>
              <p className="text-gray-400">Loading guides...</p>
            </div>
          )}
          
          {/* Blog posts grid */}
          {!isLoading && filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.div
                  key={post.id}
                  className="glass-card rounded-xl overflow-hidden border border-white/10 hover-lift transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                >
                  <Link to={`/guides/${post.id}`} className="block">
                    <div className="aspect-w-16 aspect-h-9 overflow-hidden">
                      <img 
                        src={post.image} 
                        alt={post.title} 
                        className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                      />
                    </div>
                    <div className="p-6">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.categories.map(cat => (
                          <span 
                            key={cat} 
                            className="text-xs bg-gold/10 text-gold/90 px-2 py-1 rounded-full"
                          >
                            {cat}
                          </span>
                        ))}
                      </div>
                      <h3 className="text-xl font-semibold mb-3 line-clamp-2">{post.title}</h3>
                      <p className="text-gray-400 mb-4 line-clamp-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between text-gray-400 text-sm">
                        <span>{post.date}</span>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 text-gold/80" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          ) : !isLoading && (
            <div className="text-center py-20 border border-dashed border-white/20 rounded-xl bg-white/5">
              <Tag className="h-12 w-12 mx-auto mb-4 text-gold/60" />
              <h3 className="text-2xl font-medium mb-2">No guides found</h3>
              <p className="text-gray-400 mb-6">Try adjusting your search or filter criteria</p>
              <button 
                onClick={() => {
                  setSearchQuery("");
                  setSelectedCategory(null);
                }}
                className="inline-flex items-center px-4 py-2 rounded-lg border border-gold/50 bg-gold/10 text-gold hover:bg-gold/20 transition-all duration-300"
              >
                Reset filters
              </button>
            </div>
          )}
          
          {/* Featured/Popular posts section */}
          {filteredPosts.length > 0 && (
            <div className="mt-20">
              <h2 className="text-2xl font-bold mb-6">Popular Guides</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {blogPosts
                  .filter(post => post.featured)
                  .slice(0, 2)
                  .map((post, index) => (
                    <motion.div 
                      key={post.id}
                      className="flex items-center p-4 bg-white/5 border border-white/10 rounded-lg hover:bg-white/10 transition-all"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 * index }}
                    >
                      <img 
                        src={post.image} 
                        alt={post.title} 
                        className="w-20 h-20 object-cover rounded-md mr-4"
                      />
                      <div className="flex-1">
                        <h3 className="font-medium mb-1 line-clamp-2">{post.title}</h3>
                        <div className="flex items-center text-sm text-gray-400">
                          <span>{post.date}</span>
                          <span className="mx-2">•</span>
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gold/60" />
                    </motion.div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Guides;
