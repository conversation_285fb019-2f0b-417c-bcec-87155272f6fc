import { useEffect, lazy, useState, Suspense } from "react";
import Navbar from "@/components/Navbar";
import HeroSection from "@/components/hero/HeroSection";

// Defer loading of non-critical above-the-fold content
const LogoCarousel = lazy(() => import("@/components/logo-carousel"));
const FeaturesSection = lazy(() => import("@/components/FeaturesSection"));
const LazyLoadSection = lazy(() => import("@/components/LazyLoadSection"));

// Lazy load below-the-fold components
const HowItWorks = lazy(() => import("@/components/HowItWorks"));
const TestimonialsSection = lazy(() => import("@/components/TestimonialsSection"));
// Pre-load these components since they're important for navigation
const PricingSection = lazy(() => import("@/components/PricingSection"));
const GuidesSection = lazy(() => import("@/components/GuidesSection"));
const FAQ = lazy(() => import("@/components/FAQ"));
const CTA = lazy(() => import("@/components/CTA"));
const Footer = lazy(() => import("@/components/Footer"));

export default function Index() {
  // State to handle scroll-to-section on hash change
  const [hashTarget, setHashTarget] = useState<string | null>(null);
  const [secondaryContentLoaded, setSecondaryContentLoaded] = useState(false);
  const [belowFoldReady, setBelowFoldReady] = useState(false);

  // Handle initial load and hash changes
  useEffect(() => {
    if (!window.location.hash) {
      window.scrollTo(0, 0);
    } else {
      // Set the hash target to trigger scroll effect
      setHashTarget(window.location.hash.substring(1));
    }
    
    // Add hash change listener for navigation during the session
    const handleHashChange = () => {
      if (window.location.hash) {
        setHashTarget(window.location.hash.substring(1));
      }
    };
    
    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  // Handle scrolling when hash target changes
  useEffect(() => {
    if (hashTarget) {
      // Slight delay to ensure React has fully rendered
      setTimeout(() => {
        const element = document.getElementById(hashTarget);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    }
  }, [hashTarget]);
  
  // Progressive loading of content sections
  useEffect(() => {
    // Allow secondary content to load after a short delay
    setTimeout(() => {
      setSecondaryContentLoaded(true);
    }, 300);

    // Prepare below-fold content after a longer delay
    setTimeout(() => {
      setBelowFoldReady(true);
    }, 2000);
  }, []);

  const faqs = [
    {
      question: "What type of subscriptions do you offer?",
      answer: "We offer monthly subscription access: $49.99 Monthly. Start with our 7-day free trial to experience the full power of Datawise before subscribing."
    },
    {
      question: "What sports do you cover?",
      answer: "We cover basically every sport imaginable. If we have an edge we will bet it. Football, Basketball, Baseball, Hockey, Tennis, MMA, Esports and much more."
    },
    {
      question: "What is the minimum bankroll required to use Datawise?",
      answer: "We recommend a minimum bankroll of $500 to fully utilize our platform. However, you can start with as little as $100 with adjusted unit sizes."
    },
    {
      question: "Can I cancel my subscription?",
      answer: "Yes, you can cancel your subscription anytime. Simply go to your account settings and select 'Cancel Subscription'. Your access will continue until the end of your billing period."
    },
    {
      question: "Why should I trust Datawise?",
      answer: "Our platform is built on transparent, data-driven analysis. We provide detailed insights on all picks and maintain a verifiable track record. Our models have been developed and tested over years of sports analysis."
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Above-the-fold content - eagerly loaded */}
      <Navbar />
      <HeroSection />
      
      {/* Secondary above-fold content - deferred loading */}
      {secondaryContentLoaded && (
        <Suspense fallback={null}>
          <div className="mt-2">
            <LogoCarousel />
          </div>
          <FeaturesSection />
        </Suspense>
      )}
      
      {/* Below-the-fold content - lazy loaded with intersection observer */}
      {belowFoldReady && (
        <Suspense fallback={null}>
          <LazyLoadSection id="how-it-works">
            <HowItWorks />
          </LazyLoadSection>
          
          <LazyLoadSection id="testimonials">
            <TestimonialsSection />
          </LazyLoadSection>
          
          {/* Critical sections for navigation */}
          <div id="pricing">
            <PricingSection />
          </div>
          
          <div id="faq">
            <FAQ faqs={faqs} />
          </div>
          
          <LazyLoadSection id="call-to-action">
            <CTA />
          </LazyLoadSection>
          
          <LazyLoadSection id="footer">
            <Footer />
          </LazyLoadSection>
        </Suspense>
      )}
    </div>
  );
};
