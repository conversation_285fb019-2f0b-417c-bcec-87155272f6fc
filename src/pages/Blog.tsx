
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Guides from "./Guides";

const Blog = () => {
  const location = useLocation();
  
  // For SEO purposes, we want to handle both /blog and /guides URLs
  useEffect(() => {
    // Update canonical URL to point to /blog
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = `${window.location.origin}/blog`;
    document.head.appendChild(canonical);
    
    return () => {
      document.head.removeChild(canonical);
    };
  }, []);

  // We're reusing the Guides component since it has all the blog functionality
  return <Guides />;
};

export default Blog;
